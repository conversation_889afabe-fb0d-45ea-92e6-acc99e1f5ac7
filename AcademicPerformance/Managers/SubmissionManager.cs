using Mapster;
using MongoDB.Bson;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.MongoDocuments;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Managers
{
    /// <summary>
    /// Submission management implementation - Business logic katmanı
    /// </summary>
    public class SubmissionManager : ISubmissionManager
    {
        private readonly ISubmissionStore _submissionStore;
        private readonly IFormStore _formStore;
        private readonly ICriteriaStore _criteriaStore;
        private readonly ILogger<SubmissionManager> _logger;

        public SubmissionManager(
            ISubmissionStore submissionStore,
            IFormStore formStore,
            ICriteriaStore criteriaStore,
            ILogger<SubmissionManager> logger)
        {
            _submissionStore = submissionStore;
            _formStore = formStore;
            _criteriaStore = criteriaStore;
            _logger = logger;
        }

        #region Submission Management

        public async Task<SubmissionDto?> GetSubmissionByFormIdAsync(string universityUserId, string formId)
        {
            try
            {

                var canAccess = await CanAcademicianAccessFormAsync(universityUserId, formId);
                if (!canAccess)
                {
                    _logger.LogWarning("User {UserId} cannot access form {FormId}", universityUserId, formId);
                    return null;
                }

                // Submission'ı getir
                var submission = await _submissionStore.GetSubmissionByFormIdAsync(universityUserId, formId);
                if (submission == null)
                    return null;

                // DTO'ya map et
                var dto = submission.Adapt<SubmissionDto>();

                // Completion percentage hesapla ve güncelle
                dto.CompletionPercentage = await CalculateCompletionPercentageAsync(submission.Id);

                return dto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission for form {FormId} and user {UserId}", formId, universityUserId);
                throw;
            }
        }

        public async Task<SubmissionDto?> GetSubmissionByIdAsync(string universityUserId, string submissionId)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null || submission.AcademicianUserId != universityUserId)
                    return null;


                var canAccess = await CanAcademicianAccessFormAsync(universityUserId, submission.FormId);
                if (!canAccess)
                    return null;

                var dto = submission.Adapt<SubmissionDto>();
                dto.CompletionPercentage = await CalculateCompletionPercentageAsync(submission.Id);

                return dto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission {SubmissionId} for user {UserId}", submissionId, universityUserId);
                throw;
            }
        }

        public async Task<SubmissionDto> CreateSubmissionAsync(string universityUserId, SubmissionCreateDto dto)
        {
            try
            {

                var canAccess = await CanAcademicianAccessFormAsync(universityUserId, dto.FormId);
                if (!canAccess)
                    throw new UnauthorizedAccessException($"User {universityUserId} cannot access form {dto.FormId}");

                // Zaten submission var mı kontrol et
                var existingSubmission = await _submissionStore.GetSubmissionByFormIdAsync(universityUserId, dto.FormId);
                if (existingSubmission != null)
                    throw new InvalidOperationException($"Submission already exists for form {dto.FormId}");

                // Yeni submission oluştur
                var document = new AcademicSubmissionDocument
                {
                    FormId = dto.FormId,
                    AcademicianUserId = universityUserId,
                    Status = "Draft",
                    Notes = dto.Notes,
                    CreatedAt = DateTime.UtcNow,
                    LastActivityAt = DateTime.UtcNow
                };

                var createdDocument = await _submissionStore.CreateSubmissionAsync(document);
                return createdDocument.Adapt<SubmissionDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating submission for form {FormId} and user {UserId}", dto.FormId, universityUserId);
                throw;
            }
        }

        public async Task<bool> UpdateSubmissionAsync(string universityUserId, SubmissionUpdateDto dto)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByIdAsync(dto.Id);
                if (submission == null || submission.AcademicianUserId != universityUserId)
                    return false;

                // Status kontrolü - Submitted olan submission güncellenemez
                if (submission.Status == "Submitted" || submission.Status == "UnderReview")
                {
                    _logger.LogWarning("Cannot update submission {SubmissionId} with status {Status}", dto.Id, submission.Status);
                    return false;
                }

                // Güncelleme
                submission.Notes = dto.Notes;
                submission.UpdatedAt = DateTime.UtcNow;
                submission.LastActivityAt = DateTime.UtcNow;

                // CriteriaData güncelleme (varsa)
                if (dto.CriteriaData?.Any() == true)
                {
                    submission.CriteriaData = dto.CriteriaData.Adapt<List<SubmissionCriterionData>>();
                }

                var success = await _submissionStore.UpdateSubmissionAsync(submission);

                // Completion percentage güncelle
                if (success)
                {
                    await UpdateCompletionPercentageAsync(submission.Id);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating submission {SubmissionId} for user {UserId}", dto.Id, universityUserId);
                throw;
            }
        }

        public async Task<bool> DeleteSubmissionAsync(string universityUserId, string submissionId)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null || submission.AcademicianUserId != universityUserId)
                    return false;

                // Sadece Draft status'taki submission'lar silinebilir
                if (submission.Status != "Draft")
                {
                    _logger.LogWarning("Cannot delete submission {SubmissionId} with status {Status}", submissionId, submission.Status);
                    return false;
                }

                return await _submissionStore.DeleteSubmissionAsync(submissionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting submission {SubmissionId} for user {UserId}", submissionId, universityUserId);
                throw;
            }
        }

        public async Task<bool> UpdateSubmissionStatusAsync(string universityUserId, SubmissionStatusUpdateDto dto)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByIdAsync(dto.Id);
                if (submission == null || submission.AcademicianUserId != universityUserId)
                    return false;

                // Status geçiş kontrolü
                if (!IsValidStatusTransition(submission.Status, dto.Status))
                {
                    _logger.LogWarning("Invalid status transition from {OldStatus} to {NewStatus} for submission {SubmissionId}",
                        submission.Status, dto.Status, dto.Id);
                    return false;
                }

                DateTime? submittedAt = null;
                if (dto.Status == "Submitted")
                {
                    submittedAt = DateTime.UtcNow;
                }

                return await _submissionStore.UpdateSubmissionStatusAsync(dto.Id, dto.Status, submittedAt);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating submission status {SubmissionId} to {Status} for user {UserId}",
                    dto.Id, dto.Status, universityUserId);
                throw;
            }
        }

        #endregion

        #region Criterion Data Management

        public async Task<bool> InputCriterionDataAsync(string universityUserId, string formId, string criterionLinkId, CriterionDataInputDto dto)
        {
            try
            {
                // Validation
                if (!await ValidateCriterionDataAsync(criterionLinkId, dto))
                {
                    _logger.LogWarning("Criterion data validation failed for criterion {CriterionLinkId}", criterionLinkId);
                    return false;
                }

                // Submission'ı getir veya oluştur
                var submission = await GetOrCreateSubmissionAsync(universityUserId, formId);
                if (submission == null)
                    return false;

                // Criterion bilgilerini getir
                var criterionLink = await _formStore.GetFormCriterionLinkByIdAsync(criterionLinkId);
                if (criterionLink == null)
                    return false;

                // Criterion data'sını hazırla
                var criterionData = new SubmissionCriterionData
                {
                    CriterionLinkId = criterionLinkId,
                    CriterionType = criterionLink.CriterionType,
                    CriterionName = await GetCriterionNameAsync(criterionLink),
                    DataEntries = dto.DataEntries.Adapt<List<CriterionDataEntry>>(),
                    IsCompleted = ValidateCompleteness(dto.DataEntries),
                    LastUpdated = DateTime.UtcNow,
                    Notes = dto.Notes
                };

                // Data'yı kaydet
                var success = await _submissionStore.UpsertCriterionDataAsync(submission.Id, criterionLinkId, criterionData);

                // Completion percentage güncelle
                if (success)
                {
                    await UpdateCompletionPercentageAsync(submission.Id);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inputting criterion data for criterion {CriterionLinkId} in form {FormId}",
                    criterionLinkId, formId);
                throw;
            }
        }

        public async Task<bool> UpdateCriterionDataAsync(string universityUserId, string formId, string criterionLinkId, string dataId, CriterionDataUpdateDto dto)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByFormIdAsync(universityUserId, formId);
                if (submission == null)
                    return false;

                // Status kontrolü
                if (submission.Status == "Submitted" || submission.Status == "UnderReview")
                    return false;

                // Mevcut data entry'yi bul
                var criterionData = submission.CriteriaData?.FirstOrDefault(cd => cd.CriterionLinkId == criterionLinkId);
                var dataEntry = criterionData?.DataEntries?.FirstOrDefault(de => de.Id == dataId);

                if (dataEntry == null)
                    return false;

                // Güncelle
                dataEntry.FieldName = dto.FieldName;
                dataEntry.Value = dto.Value;
                dataEntry.Description = dto.Description;
                dataEntry.UpdatedAt = DateTime.UtcNow;

                return await _submissionStore.UpdateCriterionDataEntryAsync(submission.Id, criterionLinkId, dataId, dataEntry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating criterion data {DataId} for criterion {CriterionLinkId}",
                    dataId, criterionLinkId);
                throw;
            }
        }

        public async Task<bool> DeleteCriterionDataAsync(string universityUserId, string formId, string criterionLinkId, string dataId)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByFormIdAsync(universityUserId, formId);
                if (submission == null)
                    return false;

                // Status kontrolü
                if (submission.Status == "Submitted" || submission.Status == "UnderReview")
                    return false;

                var success = await _submissionStore.DeleteCriterionDataEntryAsync(submission.Id, criterionLinkId, dataId);

                // Completion percentage güncelle
                if (success)
                {
                    await UpdateCompletionPercentageAsync(submission.Id);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting criterion data {DataId} for criterion {CriterionLinkId}",
                    dataId, criterionLinkId);
                throw;
            }
        }

        public async Task<List<CriterionDataEntryDto>> GetCriterionDataEntriesAsync(string universityUserId, string formId, string criterionLinkId)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByFormIdAsync(universityUserId, formId);
                if (submission == null)
                    return new List<CriterionDataEntryDto>();

                var dataEntries = await _submissionStore.GetCriterionDataEntriesAsync(submission.Id, criterionLinkId);
                return dataEntries?.Adapt<List<CriterionDataEntryDto>>() ?? new List<CriterionDataEntryDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting criterion data entries for criterion {CriterionLinkId}", criterionLinkId);
                throw;
            }
        }

        // Pagination Method for Criterion Data Entries
        public async Task<PagedListDto<CriterionDataEntryDto>> GetCriterionDataEntriesAsync(string universityUserId, string formId, string criterionLinkId, PagedListCo<GetStatusFilterCo> co)
        {
            try
            {
                var allDataEntries = await GetCriterionDataEntriesAsync(universityUserId, formId, criterionLinkId);

                // Filtreleme uygula
                var filteredEntries = allDataEntries.AsQueryable();

                if (!string.IsNullOrEmpty(co.Criteria?.NameContains))
                {
                    filteredEntries = filteredEntries.Where(e =>
                        (e.Value != null && e.Value.Contains(co.Criteria!.NameContains, StringComparison.OrdinalIgnoreCase)) ||
                        (e.Description != null && e.Description.Contains(co.Criteria!.NameContains, StringComparison.OrdinalIgnoreCase)));
                }

                if (co.Criteria?.CreatedAfter.HasValue == true)
                {
                    filteredEntries = filteredEntries.Where(e => e.CreatedAt >= co.Criteria!.CreatedAfter.Value);
                }

                if (co.Criteria?.CreatedBefore.HasValue == true)
                {
                    filteredEntries = filteredEntries.Where(e => e.CreatedAt <= co.Criteria!.CreatedBefore.Value);
                }

                // Sıralama uygula
                if (!string.IsNullOrEmpty(co.Sort))
                {
                    filteredEntries = co.Sort.ToLower() switch
                    {
                        "value" => filteredEntries.OrderBy(e => e.Value),
                        "value_desc" => filteredEntries.OrderByDescending(e => e.Value),
                        "created" => filteredEntries.OrderBy(e => e.CreatedAt),
                        "created_desc" => filteredEntries.OrderByDescending(e => e.CreatedAt),
                        "modified" => filteredEntries.OrderBy(e => e.LastModifiedAt),
                        "modified_desc" => filteredEntries.OrderByDescending(e => e.LastModifiedAt),
                        _ => filteredEntries.OrderByDescending(e => e.CreatedAt)
                    };
                }
                else
                {
                    filteredEntries = filteredEntries.OrderByDescending(e => e.CreatedAt);
                }

                // Pagination uygula
                var totalCount = filteredEntries.Count();
                var pagedData = filteredEntries
                    .Skip(co.Pager.Skip)
                    .Take(co.Pager.Size)
                    .ToList();

                return new PagedListDto<CriterionDataEntryDto>
                {
                    Count = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size,
                    Data = pagedData
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting paginated criterion data entries for criterion {CriterionLinkId}", criterionLinkId);
                throw;
            }
        }

        #endregion

        #region Validation and Calculation

        public async Task<decimal> CalculateCompletionPercentageAsync(string submissionId)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null)
                    return 0;

                // Form'daki toplam kriter sayısını getir
                var totalCriteria = await GetTotalCriteriaCountAsync(submission.FormId);
                if (totalCriteria == 0)
                    return 0;

                // Tamamlanan kriter sayısını hesapla
                var completedCriteria = submission.CriteriaData?.Count(cd => cd.IsCompleted) ?? 0;

                var percentage = Math.Round((decimal)completedCriteria / totalCriteria * 100, 2);

                // MongoDB'de güncelle
                await _submissionStore.UpdateCompletionPercentageAsync(submissionId, percentage);

                return percentage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating completion percentage for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        public async Task<bool> ValidateCriterionDataAsync(string criterionLinkId, CriterionDataInputDto dto)
        {
            try
            {
                // Criterion link'i getir
                var criterionLink = await _formStore.GetFormCriterionLinkByIdAsync(criterionLinkId);
                if (criterionLink == null)
                    return false;

                // Criterion type'a göre validation
                if (criterionLink.CriterionType == "Dynamic")
                {
                    return await ValidateDynamicCriterionDataAsync(criterionLink.DynamicCriterionTemplateId, dto);
                }
                else if (criterionLink.CriterionType == "Static")
                {
                    return await ValidateStaticCriterionDataAsync(criterionLink.StaticCriterionSystemId, dto);
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating criterion data for criterion {CriterionLinkId}", criterionLinkId);
                throw;
            }
        }

        public async Task<bool> CanSubmitAsync(string universityUserId, string submissionId)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null || submission.AcademicianUserId != universityUserId)
                    return false;

                // Status kontrolü
                if (submission.Status != "Draft" && submission.Status != "InProgress")
                    return false;

                // Form deadline kontrolü
                var form = await _formStore.GetEvaluationFormByIdAsync(submission.FormId);
                if (form?.SubmissionDeadline.HasValue == true && DateTime.UtcNow > form.SubmissionDeadline.Value)
                    return false;

                // Completion percentage kontrolü (minimum %100 olmalı)
                var completionPercentage = await CalculateCompletionPercentageAsync(submissionId);
                return completionPercentage >= 100;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if submission {SubmissionId} can be submitted", submissionId);
                throw;
            }
        }

        public async Task<bool> CanAcademicianAccessFormAsync(string universityUserId, string formId)
        {
            try
            {
                // Form detaylarını getir
                var form = await _formStore.GetEvaluationFormByIdAsync(formId);
                if (form == null)
                    return false;

                // Test için tüm form'lara erişim izni ver
                return true; // Test implementation
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking form access for user {UserId} and form {FormId}", universityUserId, formId);
                throw;
            }
        }

        #endregion

        #region Helper Methods

        public async Task<SubmissionDto> GetOrCreateSubmissionAsync(string universityUserId, string formId)
        {
            try
            {
                // Önce mevcut submission'ı kontrol et
                var existingSubmission = await GetSubmissionByFormIdAsync(universityUserId, formId);
                if (existingSubmission != null)
                    return existingSubmission;

                // Yoksa yeni oluştur
                var createDto = new SubmissionCreateDto { FormId = formId };
                return await CreateSubmissionAsync(universityUserId, createDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting or creating submission for form {FormId} and user {UserId}", formId, universityUserId);
                throw;
            }
        }

        public async Task<List<SubmissionSummaryDto>> GetAcademicianSubmissionsAsync(string universityUserId)
        {
            try
            {
                var submissions = await _submissionStore.GetSubmissionsByAcademicianAsync(universityUserId);
                var summaries = new List<SubmissionSummaryDto>();

                foreach (var submission in submissions)
                {
                    var form = await _formStore.GetEvaluationFormByIdAsync(submission.FormId);
                    var totalCriteria = await GetTotalCriteriaCountAsync(submission.FormId);
                    var completedCriteria = submission.CriteriaData?.Count(cd => cd.IsCompleted) ?? 0;

                    var summary = new SubmissionSummaryDto
                    {
                        Id = submission.Id,
                        FormId = submission.FormId,
                        FormTitle = form?.Name ?? "Unknown Form",
                        Status = submission.Status,
                        CompletionPercentage = submission.CompletionPercentage,
                        SubmissionDeadline = form?.SubmissionDeadline,
                        LastActivityAt = submission.LastActivityAt,
                        TotalCriteria = totalCriteria,
                        CompletedCriteria = completedCriteria,
                        IsOverdue = form?.SubmissionDeadline.HasValue == true && DateTime.UtcNow > form.SubmissionDeadline.Value
                    };

                    summaries.Add(summary);
                }

                return summaries;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting academician submissions for user {UserId}", universityUserId);
                throw;
            }
        }

        public async Task<int> GetTotalCriteriaCountAsync(string formId)
        {
            try
            {
                var categories = await _formStore.GetFormCategoriesByFormIdAsync(formId);
                var totalCriteria = 0;

                foreach (var category in categories)
                {
                    var criterionLinks = await _formStore.GetFormCriterionLinksByCategoryIdAsync(category.Id);
                    totalCriteria += criterionLinks.Count;
                }

                return totalCriteria;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total criteria count for form {FormId}", formId);
                throw;
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task UpdateCompletionPercentageAsync(string submissionId)
        {
            await CalculateCompletionPercentageAsync(submissionId);
        }

        private bool IsValidStatusTransition(string currentStatus, string newStatus)
        {
            var validTransitions = new Dictionary<string, List<string>>
            {
                ["Draft"] = new List<string> { "InProgress", "Submitted" },
                ["InProgress"] = new List<string> { "Draft", "Submitted" },
                ["Submitted"] = new List<string> { "UnderReview" },
                ["UnderReview"] = new List<string> { "Submitted" } // Geri gönderme durumu
            };

            return validTransitions.ContainsKey(currentStatus) &&
                   validTransitions[currentStatus].Contains(newStatus);
        }

        private bool ValidateCompleteness(List<CriterionDataEntryDto> dataEntries)
        {
            // Basit completeness kontrolü - en az bir data entry olmalı
            return dataEntries?.Any(de => !string.IsNullOrWhiteSpace(de.Value)) == true;
        }

        private async Task<bool> ValidateDynamicCriterionDataAsync(string? dynamicCriterionTemplateId, CriterionDataInputDto dto)
        {
            if (string.IsNullOrEmpty(dynamicCriterionTemplateId))
                return false;

            // Dynamic criterion template'i getir ve input field'larını kontrol et
            var template = await _criteriaStore.GetDynamicCriterionTemplateByIdAsync(dynamicCriterionTemplateId);
            if (template == null || template.Status != "Active")
                return false;

            // Input field validation logic burada implementasyonu yapılacak
            return true; // Şimdilik basit validation
        }

        private async Task<bool> ValidateStaticCriterionDataAsync(string? staticCriterionSystemId, CriterionDataInputDto dto)
        {
            if (string.IsNullOrEmpty(staticCriterionSystemId))
                return false;

            // Static criterion definition'ı getir ve validation kurallarını kontrol et
            var definition = await _criteriaStore.GetStaticCriterionDefinitionByIdAsync(staticCriterionSystemId);
            if (definition == null || !definition.IsActive)
                return false;

            // Static criterion validation logic burada implementasyonu yapılacak
            return true; // Şimdilik basit validation
        }

        private async Task<string> GetCriterionNameAsync(dynamic criterionLink)
        {
            try
            {
                if (criterionLink.CriterionType == "Dynamic" && !string.IsNullOrEmpty(criterionLink.DynamicCriterionTemplateId))
                {
                    var template = await _criteriaStore.GetDynamicCriterionTemplateByIdAsync(criterionLink.DynamicCriterionTemplateId);
                    return template?.Name ?? "Unknown Dynamic Criterion";
                }
                else if (criterionLink.CriterionType == "Static" && !string.IsNullOrEmpty(criterionLink.StaticCriterionSystemId))
                {
                    var definition = await _criteriaStore.GetStaticCriterionDefinitionByIdAsync(criterionLink.StaticCriterionSystemId);
                    return definition?.Name ?? "Unknown Static Criterion";
                }

                return "Unknown Criterion";
            }
            catch
            {
                return "Unknown Criterion";
            }
        }

        #endregion

     

        /// <summary>
        /// Dosyayı submission'a ve criterion'a bağla
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="criterionLinkId">Criterion Link ID'si</param>
        /// <param name="evidenceFileId">Evidence File ID'si</param>
        /// <param name="fieldName">Field adı (opsiyonel)</param>
        /// <returns>Bağlantı başarılı mı?</returns>
        public async Task<bool> AttachFileToSubmissionAsync(string submissionId, string criterionLinkId, string evidenceFileId, string? fieldName = null)
        {
            try
            {
                _logger.LogInformation($"File-criterion bağlantısı kuruluyor: Submission: {submissionId}, Criterion: {criterionLinkId}, File: {evidenceFileId}");

                // Submission'ı getir
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null)
                {
                    _logger.LogWarning($"Submission bulunamadı: {submissionId}");
                    return false;
                }

                // Criterion data'sını getir veya oluştur
                var criterionData = submission.CriteriaData?.FirstOrDefault(cd => cd.CriterionLinkId == criterionLinkId);
                if (criterionData == null)
                {
                    // Yeni criterion data oluştur
                    criterionData = new SubmissionCriterionData
                    {
                        CriterionLinkId = criterionLinkId,
                        CriterionType = "Dynamic", // Default olarak Dynamic
                        CriterionName = await GetCriterionNameAsync(await _formStore.GetFormCriterionLinkByIdAsync(criterionLinkId)),
                        DataEntries = new List<CriterionDataEntry>(),
                        IsCompleted = false,
                        LastUpdated = DateTime.UtcNow
                    };

                    if (submission.CriteriaData == null)
                        submission.CriteriaData = new List<SubmissionCriterionData>();

                    submission.CriteriaData.Add(criterionData);
                }

                // File entry'sini ekle veya güncelle
                var fileEntry = criterionData.DataEntries.FirstOrDefault(de => de.FieldName == (fieldName ?? "EvidenceFile"));
                if (fileEntry == null)
                {
                    fileEntry = new CriterionDataEntry
                    {
                        Id = ObjectId.GenerateNewId().ToString(),
                        FieldName = fieldName ?? "EvidenceFile",
                        FieldType = "File",
                        CreatedAt = DateTime.UtcNow
                    };
                    criterionData.DataEntries.Add(fileEntry);
                }

                // File ID'sini set et
                fileEntry.FileId = evidenceFileId;
                fileEntry.Value = evidenceFileId; // Value olarak da file ID'sini sakla
                fileEntry.UpdatedAt = DateTime.UtcNow;
                fileEntry.IsValid = true;

                // Criterion data'sını güncelle
                criterionData.LastUpdated = DateTime.UtcNow;
                criterionData.IsCompleted = ValidateCompleteness(criterionData.DataEntries.Adapt<List<CriterionDataEntryDto>>());

                // MongoDB'de güncelle
                var success = await _submissionStore.UpsertCriterionDataAsync(submissionId, criterionLinkId, criterionData);
                if (success)
                {
                    // Completion percentage güncelle
                    await UpdateCompletionPercentageAsync(submissionId);
                    _logger.LogInformation($"File-criterion bağlantısı başarılı: Submission: {submissionId}, Criterion: {criterionLinkId}, File: {evidenceFileId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"File-criterion bağlantısı hatası: Submission: {submissionId}, Criterion: {criterionLinkId}, File: {evidenceFileId}");
                return false;
            }
        }

        /// <summary>
        /// Submission'dan dosyayı kaldır
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="criterionLinkId">Criterion Link ID'si</param>
        /// <param name="evidenceFileId">Evidence File ID'si</param>
        /// <returns>Kaldırma başarılı mı?</returns>
        public async Task<bool> DetachFileFromSubmissionAsync(string submissionId, string criterionLinkId, string evidenceFileId)
        {
            try
            {
                _logger.LogInformation($"File-criterion bağlantısı kaldırılıyor: Submission: {submissionId}, Criterion: {criterionLinkId}, File: {evidenceFileId}");

                // Submission'ı getir
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null)
                {
                    _logger.LogWarning($"Submission bulunamadı: {submissionId}");
                    return false;
                }

                // Criterion data'sını bul
                var criterionData = submission.CriteriaData?.FirstOrDefault(cd => cd.CriterionLinkId == criterionLinkId);
                if (criterionData == null)
                {
                    _logger.LogWarning($"Criterion data bulunamadı: {criterionLinkId}");
                    return false;
                }

                // File entry'sini bul ve kaldır
                var fileEntry = criterionData.DataEntries.FirstOrDefault(de => de.FileId == evidenceFileId);
                if (fileEntry != null)
                {
                    criterionData.DataEntries.Remove(fileEntry);
                    criterionData.LastUpdated = DateTime.UtcNow;
                    criterionData.IsCompleted = ValidateCompleteness(criterionData.DataEntries.Adapt<List<CriterionDataEntryDto>>());

                    // MongoDB'de güncelle
                    var success = await _submissionStore.UpsertCriterionDataAsync(submissionId, criterionLinkId, criterionData);
                    if (success)
                    {
                        // Completion percentage güncelle
                        await UpdateCompletionPercentageAsync(submissionId);
                        _logger.LogInformation($"File-criterion bağlantısı kaldırıldı: Submission: {submissionId}, Criterion: {criterionLinkId}, File: {evidenceFileId}");
                    }

                    return success;
                }

                _logger.LogWarning($"File entry bulunamadı: {evidenceFileId}");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"File-criterion bağlantısı kaldırma hatası: Submission: {submissionId}, Criterion: {criterionLinkId}, File: {evidenceFileId}");
                return false;
            }
        }

        /// <summary>
        /// Submission'ın tüm dosyalarını getir
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <returns>Dosya listesi</returns>
        public async Task<List<SubmissionFileDto>> GetSubmissionFilesAsync(string submissionId)
        {
            try
            {
                _logger.LogInformation($"Submission dosyaları getiriliyor: {submissionId}");

                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null)
                {
                    return new List<SubmissionFileDto>();
                }

                var files = new List<SubmissionFileDto>();

                if (submission.CriteriaData != null)
                {
                    foreach (var criterionData in submission.CriteriaData)
                    {
                        foreach (var dataEntry in criterionData.DataEntries.Where(de => !string.IsNullOrEmpty(de.FileId)))
                        {
                            files.Add(new SubmissionFileDto
                            {
                                FileId = dataEntry.FileId!,
                                FieldName = dataEntry.FieldName,
                                CriterionLinkId = criterionData.CriterionLinkId,
                                CriterionName = criterionData.CriterionName,
                                Description = dataEntry.Description,
                                UploadedAt = dataEntry.CreatedAt,
                                LastModifiedAt = dataEntry.UpdatedAt
                            });
                        }
                    }
                }

                _logger.LogInformation($"Submission dosyaları getirildi: {submissionId}, Count: {files.Count}");
                return files;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Submission dosyaları getirme hatası: {submissionId}");
                return new List<SubmissionFileDto>();
            }
        }

  
    }
}
