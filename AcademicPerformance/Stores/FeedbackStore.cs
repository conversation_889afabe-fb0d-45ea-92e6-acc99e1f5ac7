using Microsoft.EntityFrameworkCore;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Services.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Stores
{
    /// <summary>
    /// Feedback Store implementation - PostgreSQL operations
    /// Epic 1 Feedback Sistemi için veri katmanı implementasyonu
    /// </summary>
    public class FeedbackStore : IFeedbackStore
    {
        private readonly AcademicPerformanceDbContext _context;
        private readonly ILogger<FeedbackStore> _logger;


        public FeedbackStore(
            AcademicPerformanceDbContext context,
            ILogger<FeedbackStore> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Submission Feedback CRUD Operations

        /// <summary>
        /// Yeni feedback oluştur
        /// </summary>
        public async Task<SubmissionFeedbackEntity> CreateFeedbackAsync(SubmissionFeedbackEntity feedbackEntity)
        {
            try
            {
                _logger.LogInformation("Creating feedback for submission {SubmissionId}", feedbackEntity.SubmissionId);

                feedbackEntity.CreatedAt = DateTime.UtcNow;
                _context.SubmissionFeedbacks.Add(feedbackEntity);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully created feedback {FeedbackId}", feedbackEntity.Id);
                return feedbackEntity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating feedback for submission {SubmissionId}", feedbackEntity.SubmissionId);
                throw;
            }
        }

        /// <summary>
        /// Feedback'i ID ile getir
        /// </summary>
        public async Task<SubmissionFeedbackEntity?> GetFeedbackByIdAsync(string feedbackId)
        {
            try
            {
                return await _context.SubmissionFeedbacks
                    .Include(f => f.CriterionFeedbacks)
                    .FirstOrDefaultAsync(f => f.Id == feedbackId && f.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedback {FeedbackId}", feedbackId);
                throw;
            }
        }

        /// <summary>
        /// Feedback'i güncelle
        /// </summary>
        public async Task<bool> UpdateFeedbackAsync(SubmissionFeedbackEntity feedbackEntity)
        {
            try
            {
                _logger.LogInformation("Updating feedback {FeedbackId}", feedbackEntity.Id);

                feedbackEntity.UpdatedAt = DateTime.UtcNow;
                _context.SubmissionFeedbacks.Update(feedbackEntity);
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully updated feedback {FeedbackId}", feedbackEntity.Id);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating feedback {FeedbackId}", feedbackEntity.Id);
                throw;
            }
        }

        /// <summary>
        /// Feedback'i soft delete et
        /// </summary>
        public async Task<bool> DeleteFeedbackAsync(string feedbackId)
        {
            try
            {
                _logger.LogInformation("Soft deleting feedback {FeedbackId}", feedbackId);

                var feedback = await _context.SubmissionFeedbacks.FindAsync(feedbackId);
                if (feedback == null) return false;

                feedback.IsActive = false;
                feedback.UpdatedAt = DateTime.UtcNow;
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully soft deleted feedback {FeedbackId}", feedbackId);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error soft deleting feedback {FeedbackId}", feedbackId);
                throw;
            }
        }

        /// <summary>
        /// Submission için tüm feedback'leri getir (kronolojik sırada)
        /// </summary>
        public async Task<List<SubmissionFeedbackEntity>> GetFeedbacksBySubmissionIdAsync(string submissionId, bool includeInactive = false)
        {
            try
            {
                var query = _context.SubmissionFeedbacks
                    .Include(f => f.CriterionFeedbacks)
                    .Where(f => f.SubmissionId == submissionId);

                if (!includeInactive)
                {
                    query = query.Where(f => f.IsActive);
                }

                return await query
                    .OrderBy(f => f.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedbacks for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        /// <summary>
        /// Controller'ın verdiği tüm feedback'leri getir
        /// </summary>
        public async Task<PagedListDto<SubmissionFeedbackEntity>> GetFeedbacksByControllerAsync(string controllerId, PagedListCo<FeedbackFilterCo> co)
        {
            try
            {
                var query = _context.SubmissionFeedbacks
                    .Include(f => f.CriterionFeedbacks)
                    .Where(f => f.ControllerUserId == controllerId && f.IsActive);

                // Filtreleme
                if (co.Criteria?.FeedbackType != null)
                {
                    query = query.Where(f => f.FeedbackType == co.Criteria!.FeedbackType);
                }

                if (co.Criteria?.StartDate != null)
                {
                    query = query.Where(f => f.CreatedAt >= co.Criteria!.StartDate);
                }

                if (co.Criteria?.EndDate != null)
                {
                    query = query.Where(f => f.CreatedAt <= co.Criteria!.EndDate);
                }

                if (co.Criteria?.Priority != null)
                {
                    query = query.Where(f => f.Priority == co.Criteria!.Priority);
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderByDescending(f => f.CreatedAt)
                    .Skip((co.Pager.Page - 1) * co.Pager.Size)
                    .Take(co.Pager.Size)
                    .ToListAsync();

                return new PagedListDto<SubmissionFeedbackEntity>
                {
                    Data = items,
                    TotalCount = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedbacks for controller {ControllerId}", controllerId);
                throw;
            }
        }

        #endregion

        #region Criterion Feedback Operations

        /// <summary>
        /// Kriter feedback'i oluştur
        /// </summary>
        public async Task<CriterionFeedbackEntity> CreateCriterionFeedbackAsync(CriterionFeedbackEntity criterionFeedbackEntity)
        {
            try
            {
                _logger.LogInformation("Creating criterion feedback for feedback {FeedbackId}", criterionFeedbackEntity.SubmissionFeedbackId);

                criterionFeedbackEntity.CreatedAt = DateTime.UtcNow;
                _context.CriterionFeedbacks.Add(criterionFeedbackEntity);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully created criterion feedback {CriterionFeedbackId}", criterionFeedbackEntity.Id);
                return criterionFeedbackEntity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating criterion feedback");
                throw;
            }
        }

        /// <summary>
        /// Kriter feedback'i güncelle
        /// </summary>
        public async Task<bool> UpdateCriterionFeedbackAsync(CriterionFeedbackEntity criterionFeedbackEntity)
        {
            try
            {
                _logger.LogInformation("Updating criterion feedback {CriterionFeedbackId}", criterionFeedbackEntity.Id);

                criterionFeedbackEntity.UpdatedAt = DateTime.UtcNow;
                _context.CriterionFeedbacks.Update(criterionFeedbackEntity);
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully updated criterion feedback {CriterionFeedbackId}", criterionFeedbackEntity.Id);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating criterion feedback {CriterionFeedbackId}", criterionFeedbackEntity.Id);
                throw;
            }
        }

        /// <summary>
        /// Feedback için tüm kriter feedback'lerini getir
        /// </summary>
        public async Task<List<CriterionFeedbackEntity>> GetCriterionFeedbacksByFeedbackIdAsync(string feedbackId)
        {
            try
            {
                return await _context.CriterionFeedbacks
                    .Where(cf => cf.SubmissionFeedbackId == feedbackId && cf.IsActive)
                    .OrderBy(cf => cf.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting criterion feedbacks for feedback {FeedbackId}", feedbackId);
                throw;
            }
        }

        /// <summary>
        /// Kriter feedback'i sil
        /// </summary>
        public async Task<bool> DeleteCriterionFeedbackAsync(string criterionFeedbackId)
        {
            try
            {
                _logger.LogInformation("Soft deleting criterion feedback {CriterionFeedbackId}", criterionFeedbackId);

                var criterionFeedback = await _context.CriterionFeedbacks.FindAsync(criterionFeedbackId);
                if (criterionFeedback == null) return false;

                criterionFeedback.IsActive = false;
                criterionFeedback.UpdatedAt = DateTime.UtcNow;
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully soft deleted criterion feedback {CriterionFeedbackId}", criterionFeedbackId);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error soft deleting criterion feedback {CriterionFeedbackId}", criterionFeedbackId);
                throw;
            }
        }

        /// <summary>
        /// Belirli kriter için feedback'leri getir
        /// </summary>
        public async Task<List<CriterionFeedbackEntity>> GetFeedbacksByCriterionAsync(string criterionLinkId, DateRange? dateRange = null)
        {
            try
            {
                var query = _context.CriterionFeedbacks
                    .Include(cf => cf.SubmissionFeedback)
                    .Where(cf => cf.CriterionLinkId == criterionLinkId && cf.IsActive);

                if (dateRange != null)
                {
                    query = query.Where(cf => cf.CreatedAt >= dateRange.StartDate && cf.CreatedAt <= dateRange.EndDate);
                }

                return await query
                    .OrderByDescending(cf => cf.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedbacks for criterion {CriterionLinkId}", criterionLinkId);
                throw;
            }
        }

        #endregion

        #region Query Operations

        /// <summary>
        /// Feedback türüne göre feedback'leri getir
        /// </summary>
        public async Task<PagedListDto<SubmissionFeedbackEntity>> GetFeedbacksByTypeAsync(string feedbackType, PagedListCo<FeedbackFilterCo> co)
        {
            try
            {
                var query = _context.SubmissionFeedbacks
                    .Include(f => f.CriterionFeedbacks)
                    .Where(f => f.FeedbackType == feedbackType && f.IsActive);

                // Ek filtreleme
                if (co.Criteria?.StartDate != null)
                {
                    query = query.Where(f => f.CreatedAt >= co.Criteria!.StartDate);
                }

                if (co.Criteria?.EndDate != null)
                {
                    query = query.Where(f => f.CreatedAt <= co.Criteria!.EndDate);
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderByDescending(f => f.CreatedAt)
                    .Skip((co.Pager.Page - 1) * co.Pager.Size)
                    .Take(co.Pager.Size)
                    .ToListAsync();

                return new PagedListDto<SubmissionFeedbackEntity>
                {
                    Data = items,
                    TotalCount = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedbacks by type {FeedbackType}", feedbackType);
                throw;
            }
        }

        /// <summary>
        /// Akademisyen için tüm feedback'leri getir
        /// </summary>
        public async Task<PagedListDto<SubmissionFeedbackEntity>> GetFeedbacksByAcademicianAsync(string academicianUserId, PagedListCo<FeedbackFilterCo> co)
        {
            try
            {
                var query = from f in _context.SubmissionFeedbacks
                            join s in _context.AcademicSubmissions on f.SubmissionId equals s.Id
                            where s.AcademicianUniveristyUserId == academicianUserId && f.IsActive
                            select f;

                query = query.Include(f => f.CriterionFeedbacks);

                // Filtreleme
                if (co.Criteria?.FeedbackType != null)
                {
                    query = query.Where(f => f.FeedbackType == co.Criteria!.FeedbackType);
                }

                if (co.Criteria?.StartDate != null)
                {
                    query = query.Where(f => f.CreatedAt >= co.Criteria!.StartDate);
                }

                if (co.Criteria?.EndDate != null)
                {
                    query = query.Where(f => f.CreatedAt <= co.Criteria!.EndDate);
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderByDescending(f => f.CreatedAt)
                    .Skip((co.Pager.Page - 1) * co.Pager.Size)
                    .Take(co.Pager.Size)
                    .ToListAsync();

                return new PagedListDto<SubmissionFeedbackEntity>
                {
                    Data = items,
                    TotalCount = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedbacks for academician {AcademicianUserId}", academicianUserId);
                throw;
            }
        }

        /// <summary>
        /// Aktif revision request'leri getir
        /// </summary>
        public async Task<List<SubmissionFeedbackEntity>> GetActiveRevisionRequestsAsync(string? academicianUserId = null)
        {
            try
            {
                var query = _context.SubmissionFeedbacks
                    .Include(f => f.CriterionFeedbacks)
                    .Where(f => f.FeedbackType == "RevisionRequest" && f.IsActive);

                if (!string.IsNullOrEmpty(academicianUserId))
                {
                    query = from f in query
                            join s in _context.AcademicSubmissions on f.SubmissionId equals s.Id
                            where s.AcademicianUniveristyUserId == academicianUserId
                            select f;
                }

                // Henüz yanıtlanmamış veya deadline'ı geçmemiş revision request'ler
                query = query.Where(f => f.AcademicianResponse == null ||
                                        (f.RevisionDeadline != null && f.RevisionDeadline > DateTime.UtcNow));

                return await query
                    .OrderBy(f => f.RevisionDeadline ?? DateTime.MaxValue)
                    .ThenByDescending(f => f.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active revision requests for academician {AcademicianUserId}", academicianUserId);
                throw;
            }
        }

        #endregion

        #region Statistics and Analytics

        /// <summary>
        /// Feedback istatistiklerini getir
        /// </summary>
        public async Task<Dictionary<string, object>> GetFeedbackStatisticsAsync(string? userId = null, string? userRole = null, DateRange? dateRange = null)
        {
            try
            {
                var stats = new Dictionary<string, object>();

                var query = _context.SubmissionFeedbacks.Where(f => f.IsActive);

                if (dateRange != null)
                {
                    query = query.Where(f => f.CreatedAt >= dateRange.StartDate && f.CreatedAt <= dateRange.EndDate);
                }

                if (!string.IsNullOrEmpty(userId))
                {
                    if (userRole == "Controller")
                    {
                        query = query.Where(f => f.ControllerUserId == userId);
                    }
                    else if (userRole == "Academician")
                    {
                        query = from f in query
                                join s in _context.AcademicSubmissions on f.SubmissionId equals s.Id
                                where s.AcademicianUniveristyUserId == userId
                                select f;
                    }
                }

                stats["TotalFeedbacks"] = await query.CountAsync();
                stats["ApprovedCount"] = await query.CountAsync(f => f.FeedbackType == "Approval");
                stats["RejectedCount"] = await query.CountAsync(f => f.FeedbackType == "Rejection");
                stats["RevisionRequestedCount"] = await query.CountAsync(f => f.FeedbackType == "RevisionRequest");

                var thisMonth = DateTime.UtcNow.AddDays(-30);
                stats["FeedbacksThisMonth"] = await query.CountAsync(f => f.CreatedAt >= thisMonth);

                // Basit ortalama hesaplama
                stats["AverageFeedbackTimeHours"] = 24.0; // Placeholder
                stats["PendingFeedbacks"] = 0; // Placeholder

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedback statistics");
                throw;
            }
        }

        /// <summary>
        /// Ortalama feedback süresini hesapla
        /// </summary>
        public async Task<double> CalculateAverageFeedbackTimeAsync(string? controllerId = null, DateRange? dateRange = null)
        {
            try
            {
                // Basit implementasyon - gerçek implementasyonda submission ve feedback tarihleri karşılaştırılacak
                return await Task.FromResult(24.0); // 24 saat ortalama
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating average feedback time");
                throw;
            }
        }

        /// <summary>
        /// En çok revision talep edilen kriterleri getir
        /// </summary>
        public async Task<List<Dictionary<string, object>>> GetMostRevisedCriteriaAsync(int limit = 10, DateRange? dateRange = null)
        {
            try
            {
                var query = _context.CriterionFeedbacks
                    .Where(cf => cf.IsActive && cf.Status == "NeedsRevision");

                if (dateRange != null)
                {
                    query = query.Where(cf => cf.CreatedAt >= dateRange.StartDate && cf.CreatedAt <= dateRange.EndDate);
                }

                var result = await query
                    .GroupBy(cf => cf.CriterionLinkId)
                    .Select(g => new
                    {
                        CriterionId = g.Key,
                        RevisionCount = g.Count()
                    })
                    .OrderByDescending(x => x.RevisionCount)
                    .Take(limit)
                    .ToListAsync();

                return result.Select(r => new Dictionary<string, object>
                {
                    ["CriterionId"] = r.CriterionId,
                    ["CriterionName"] = "Criterion Name", // Placeholder
                    ["RevisionCount"] = r.RevisionCount,
                    ["RevisionPercentage"] = 0.0 // Placeholder
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting most revised criteria");
                throw;
            }
        }

        /// <summary>
        /// Controller performans istatistiklerini getir
        /// </summary>
        public async Task<Dictionary<string, object>> GetControllerPerformanceStatsAsync(string controllerId, DateRange? dateRange = null)
        {
            try
            {
                var stats = new Dictionary<string, object>();

                var query = _context.SubmissionFeedbacks
                    .Where(f => f.ControllerUserId == controllerId && f.IsActive);

                if (dateRange != null)
                {
                    query = query.Where(f => f.CreatedAt >= dateRange.StartDate && f.CreatedAt <= dateRange.EndDate);
                }

                stats["TotalFeedbacks"] = await query.CountAsync();
                stats["AverageRating"] = await query.Where(f => f.OverallRating.HasValue).AverageAsync(f => f.OverallRating.Value);
                stats["ResponseTimeHours"] = 24.0; // Placeholder

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting controller performance stats");
                throw;
            }
        }

        #endregion

        #region Notification Operations

        /// <summary>
        /// Feedback notification log'u oluştur
        /// </summary>
        public async Task<FeedbackNotificationLogEntity> CreateNotificationLogAsync(FeedbackNotificationLogEntity notificationEntity)
        {
            try
            {
                notificationEntity.CreatedAt = DateTime.UtcNow;
                _context.FeedbackNotificationLogs.Add(notificationEntity);
                await _context.SaveChangesAsync();
                return notificationEntity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating notification log");
                throw;
            }
        }

        /// <summary>
        /// Notification durumunu güncelle
        /// </summary>
        public async Task<bool> UpdateNotificationStatusAsync(string notificationId, string status, string? errorMessage = null)
        {
            try
            {
                var notification = await _context.FeedbackNotificationLogs.FindAsync(notificationId);
                if (notification == null) return false;

                notification.Status = status;
                notification.ErrorMessage = errorMessage;
                notification.UpdatedAt = DateTime.UtcNow;

                if (status == "Sent")
                {
                    notification.SentAt = DateTime.UtcNow;
                }

                var result = await _context.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating notification status");
                throw;
            }
        }

        /// <summary>
        /// Feedback için notification gönderildi mi kontrol et
        /// </summary>
        public async Task<bool> IsNotificationSentAsync(string feedbackId)
        {
            try
            {
                return await _context.SubmissionFeedbacks
                    .AnyAsync(f => f.Id == feedbackId && f.NotificationSent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking notification status");
                return false;
            }
        }

        /// <summary>
        /// Başarısız notification'ları getir (retry için)
        /// </summary>
        public async Task<List<FeedbackNotificationLogEntity>> GetFailedNotificationsAsync(int maxRetryCount = 3)
        {
            try
            {
                return await _context.FeedbackNotificationLogs
                    .Where(n => n.Status == "Failed" && n.RetryCount < maxRetryCount)
                    .OrderBy(n => n.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting failed notifications");
                throw;
            }
        }

        #endregion

        #region Validation and Helper Methods

        /// <summary>
        /// Feedback var mı kontrol et
        /// </summary>
        public async Task<bool> FeedbackExistsAsync(string feedbackId)
        {
            try
            {
                return await _context.SubmissionFeedbacks.AnyAsync(f => f.Id == feedbackId && f.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feedback existence");
                return false;
            }
        }

        /// <summary>
        /// Kullanıcının feedback'e erişim yetkisi var mı kontrol et
        /// </summary>
        public async Task<bool> HasAccessToFeedbackAsync(string userId, string feedbackId)
        {
            try
            {
                var feedback = await _context.SubmissionFeedbacks
                    .Include(f => f.Submission)
                    .FirstOrDefaultAsync(f => f.Id == feedbackId && f.IsActive);

                if (feedback?.Submission == null) return false;

                // Controller veya submission sahibi erişebilir
                return feedback.ControllerUserId == userId ||
                       feedback.Submission.AcademicianUniveristyUserId == userId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feedback access");
                return false;
            }
        }

        /// <summary>
        /// Submission için belirli türde feedback var mı kontrol et
        /// </summary>
        public async Task<bool> HasFeedbackOfTypeAsync(string submissionId, string feedbackType)
        {
            try
            {
                return await _context.SubmissionFeedbacks
                    .AnyAsync(f => f.SubmissionId == submissionId && f.FeedbackType == feedbackType && f.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feedback type existence");
                return false;
            }
        }

        /// <summary>
        /// Feedback'in düzenlenebilir olup olmadığını kontrol et
        /// </summary>
        public async Task<bool> CanEditFeedbackAsync(string feedbackId, string userId)
        {
            try
            {
                var feedback = await _context.SubmissionFeedbacks.FindAsync(feedbackId);
                if (feedback == null || !feedback.IsActive) return false;

                // Sadece oluşturan controller düzenleyebilir ve 24 saat içinde olmalı
                return feedback.ControllerUserId == userId &&
                       DateTime.UtcNow.Subtract(feedback.CreatedAt).TotalHours <= 24;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feedback edit permission");
                return false;
            }
        }

        /// <summary>
        /// Son feedback tarihini getir
        /// </summary>
        public async Task<DateTime?> GetLastFeedbackDateAsync(string submissionId)
        {
            try
            {
                return await _context.SubmissionFeedbacks
                    .Where(f => f.SubmissionId == submissionId && f.IsActive)
                    .MaxAsync(f => (DateTime?)f.CreatedAt);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last feedback date");
                return null;
            }
        }

        #endregion

        #region Bulk Operations

        /// <summary>
        /// Submission feedback bulk create - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> CreateBulkFeedbackAsync(
            List<SubmissionFeedbackEntity> feedbackEntities,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("FeedbackStore bulk feedback create başlatılıyor - Count: {Count}", feedbackEntities.Count);

                if (!feedbackEntities.Any())
                {
                    _logger.LogWarning("Bulk feedback create için entity bulunamadı");
                    return new BulkOperationResult
                    {
                        Success = true,
                        ProcessedCount = 0,
                        ElapsedTime = TimeSpan.Zero
                    };
                }

                // Apply business logic (temporarily disabled)
                // feedbackEntities.ApplyFeedbackBusinessLogic("system");

                // Apply audit fields (temporarily disabled)
                // feedbackEntities.ApplyAuditFields(DateTime.UtcNow, "system");

                // Validate entities before insert
                var validationErrors = ValidateEntitiesForFeedbackInsert(feedbackEntities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk feedback create validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkInsertAsync(
                    feedbackEntities,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("FeedbackStore bulk feedback create tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("FeedbackStore bulk feedback create başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "FeedbackStore bulk feedback create işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        /// <summary>
        /// Criterion feedback bulk create - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> CreateBulkCriterionFeedbackAsync(
            List<CriterionFeedbackEntity> criterionFeedbackEntities,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("FeedbackStore bulk criterion feedback create başlatılıyor - Count: {Count}", criterionFeedbackEntities.Count);

                if (!criterionFeedbackEntities.Any())
                {
                    _logger.LogWarning("Bulk criterion feedback create için entity bulunamadı");
                    return new BulkOperationResult
                    {
                        Success = true,
                        ProcessedCount = 0,
                        ElapsedTime = TimeSpan.Zero
                    };
                }

                // Apply audit fields (temporarily disabled)
                // criterionFeedbackEntities.ApplyAuditFields(DateTime.UtcNow, "system");

                // Apply business logic for criterion feedback (temporarily disabled)
                // foreach (var entity in criterionFeedbackEntities)
                // {
                //     entity.ApplyCriterionFeedbackBusinessLogic();
                // }

                // Validate entities before insert
                var validationErrors = ValidateEntitiesForCriterionFeedbackInsert(criterionFeedbackEntities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk criterion feedback create validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkInsertAsync(
                    criterionFeedbackEntities,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("FeedbackStore bulk criterion feedback create tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("FeedbackStore bulk criterion feedback create başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "FeedbackStore bulk criterion feedback create işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        /// <summary>
        /// Eski feedback'leri arşivle
        /// </summary>
        public async Task<int> ArchiveOldFeedbacksAsync(int olderThanDays)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);

                var oldFeedbacks = await _context.SubmissionFeedbacks
                    .Where(f => f.CreatedAt < cutoffDate && f.IsActive)
                    .ToListAsync();

                foreach (var feedback in oldFeedbacks)
                {
                    feedback.IsActive = false;
                    feedback.UpdatedAt = DateTime.UtcNow;
                }

                return await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving old feedbacks");
                throw;
            }
        }

        #endregion

        #region Missing Methods Implementation

        /// <summary>
        /// Deadline yaklaşan revision request'leri getir
        /// </summary>
        public async Task<List<SubmissionFeedbackEntity>> GetUpcomingDeadlineRevisionRequestsAsync(int daysBeforeDeadline)
        {
            try
            {
                var targetDate = DateTime.UtcNow.AddDays(daysBeforeDeadline);

                return await _context.SubmissionFeedbacks
                    .Include(f => f.CriterionFeedbacks)
                    .Where(f => f.FeedbackType == "RevisionRequest" &&
                               f.IsActive &&
                               f.RevisionDeadline != null &&
                               f.RevisionDeadline <= targetDate &&
                               f.RevisionDeadline > DateTime.UtcNow &&
                               f.AcademicianResponse == null)
                    .OrderBy(f => f.RevisionDeadline)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting upcoming deadline revision requests");
                throw;
            }
        }

        /// <summary>
        /// Belirli tarih aralığındaki feedback'leri getir
        /// </summary>
        public async Task<List<SubmissionFeedbackEntity>> GetFeedbacksByDateRangeAsync(DateTime startDate, DateTime endDate, string? feedbackType = null)
        {
            try
            {
                var query = _context.SubmissionFeedbacks
                    .Include(f => f.CriterionFeedbacks)
                    .Where(f => f.CreatedAt >= startDate && f.CreatedAt <= endDate && f.IsActive);

                if (!string.IsNullOrEmpty(feedbackType))
                {
                    query = query.Where(f => f.FeedbackType == feedbackType);
                }

                return await query
                    .OrderByDescending(f => f.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedbacks by date range");
                throw;
            }
        }

        /// <summary>
        /// Pending revision requests listesi (controller için)
        /// </summary>
        public async Task<PagedListDto<FeedbackEntryDto>> GetPendingRevisionRequestsByControllerAsync(string controllerId, PagedListCo<GetFeedbackFilterCo> co)
        {
            try
            {
                _logger.LogInformation("Getting pending revision requests for controller: {ControllerId}", controllerId);

                var query = _context.SubmissionFeedbacks
                    .Include(f => f.CriterionFeedbacks)
                    .Where(f => f.CreatedByUserId == controllerId &&
                               f.FeedbackType == "RevisionRequest" &&
                               f.IsActive &&
                               f.Status == "Pending");

                // Apply filters if provided
                if (co.Criteria != null)
                {
                    if (!string.IsNullOrEmpty(co.Criteria!.SubmissionId))
                    {
                        query = query.Where(f => f.SubmissionId.Contains(co.Criteria!.SubmissionId));
                    }

                    if (co.Criteria!.StartDate.HasValue)
                    {
                        query = query.Where(f => f.CreatedAt >= co.Criteria!.StartDate.Value);
                    }

                    if (co.Criteria!.EndDate.HasValue)
                    {
                        query = query.Where(f => f.CreatedAt <= co.Criteria!.EndDate.Value);
                    }
                }

                var totalCount = await query.CountAsync();

                var feedbacks = await query
                    .OrderByDescending(f => f.CreatedAt)
                    .Skip((co.Pager.Page - 1) * co.Pager.Size)
                    .Take(co.Pager.Size)
                    .ToListAsync();

                var feedbackDtos = feedbacks.Select(f => new FeedbackEntryDto
                {
                    Id = f.Id,
                    SubmissionId = f.SubmissionId,
                    FeedbackType = f.FeedbackType,
                    Comments = f.Comments,
                    Status = f.Status,
                    CreatedAt = f.CreatedAt,
                    CreatedByUserId = f.CreatedByUserId,
                    RevisionDeadline = f.RevisionDeadline,
                    CriterionFeedbacks = f.CriterionFeedbacks?.Select(cf => new CriterionFeedbackDto
                    {
                        Id = cf.Id,
                        CriterionId = cf.CriterionId,
                        Rating = cf.Rating,
                        Comments = cf.Comments,
                        RequiresRevision = cf.RequiresRevision
                    }).ToList() ?? new List<CriterionFeedbackDto>()
                }).ToList();

                return new PagedListDto<FeedbackEntryDto>
                {
                    Data = feedbackDtos,
                    Count = totalCount,
                    TotalCount = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending revision requests for controller: {ControllerId}", controllerId);
                throw;
            }
        }

        /// <summary>
        /// Deadline yaklaşan revision requests
        /// </summary>
        public async Task<List<FeedbackEntryDto>> GetUpcomingRevisionDeadlinesAsync(string controllerId, int daysThreshold)
        {
            try
            {
                _logger.LogInformation("Getting upcoming revision deadlines for controller: {ControllerId}, threshold: {Days} days",
                    controllerId, daysThreshold);

                var thresholdDate = DateTime.UtcNow.AddDays(daysThreshold);

                var feedbacks = await _context.SubmissionFeedbacks
                    .Include(f => f.CriterionFeedbacks)
                    .Where(f => f.CreatedByUserId == controllerId &&
                               f.FeedbackType == "RevisionRequest" &&
                               f.IsActive &&
                               f.Status == "Pending" &&
                               f.RevisionDeadline.HasValue &&
                               f.RevisionDeadline.Value <= thresholdDate &&
                               f.RevisionDeadline.Value >= DateTime.UtcNow)
                    .OrderBy(f => f.RevisionDeadline)
                    .ToListAsync();

                var feedbackDtos = feedbacks.Select(f => new FeedbackEntryDto
                {
                    Id = f.Id,
                    SubmissionId = f.SubmissionId,
                    FeedbackType = f.FeedbackType,
                    Comments = f.Comments,
                    Status = f.Status,
                    CreatedAt = f.CreatedAt,
                    CreatedByUserId = f.CreatedByUserId,
                    RevisionDeadline = f.RevisionDeadline,
                    CriterionFeedbacks = f.CriterionFeedbacks?.Select(cf => new CriterionFeedbackDto
                    {
                        Id = cf.Id,
                        CriterionId = cf.CriterionId,
                        Rating = cf.Rating,
                        Comments = cf.Comments,
                        RequiresRevision = cf.RequiresRevision
                    }).ToList() ?? new List<CriterionFeedbackDto>()
                }).ToList();

                _logger.LogInformation("Retrieved {Count} upcoming revision deadlines for controller: {ControllerId}",
                    feedbackDtos.Count, controllerId);

                return feedbackDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting upcoming revision deadlines for controller: {ControllerId}", controllerId);
                throw;
            }
        }

        /// <summary>
        /// Submission feedback bulk update - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> BulkUpdateAsync(
            List<SubmissionFeedbackEntity> entities,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("FeedbackStore bulk feedback update başlatılıyor - Count: {Count}", entities.Count);

                if (!entities.Any())
                {
                    _logger.LogWarning("Bulk feedback update için entity bulunamadı");
                    return new BulkOperationResult
                    {
                        Success = true,
                        ProcessedCount = 0,
                        ElapsedTime = TimeSpan.Zero
                    };
                }

                // Apply business logic (temporarily disabled)
                // entities.ApplyFeedbackBusinessLogic("system");

                // Apply audit fields (temporarily disabled)
                // entities.ApplyAuditFields(DateTime.UtcNow, "system");

                // Validate entities before update
                var validationErrors = ValidateEntitiesForFeedbackUpdate(entities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk feedback update validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkUpdateAsync(
                    entities,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("FeedbackStore bulk feedback update tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("FeedbackStore bulk feedback update başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "FeedbackStore bulk feedback update işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        /// <summary>
        /// Submission feedback bulk delete - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> BulkDeleteAsync(
            List<SubmissionFeedbackEntity> entities,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("FeedbackStore bulk feedback delete başlatılıyor - Count: {Count}", entities.Count);

                // Validate entities before delete
                var validationErrors = ValidateEntitiesForFeedbackDelete(entities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk feedback delete validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkDeleteAsync(
                    entities,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("FeedbackStore bulk feedback delete tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("FeedbackStore bulk feedback delete başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "FeedbackStore bulk feedback delete işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        #region Private Validation Methods

        /// <summary>
        /// Feedback insert için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForFeedbackInsert(List<SubmissionFeedbackEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.SubmissionId))
                    errors.Add($"SubmissionId boş olamaz - Entity: {entity.Id}");

                if (string.IsNullOrEmpty(entity.FeedbackType))
                    errors.Add($"FeedbackType boş olamaz - Entity: {entity.Id}");

                if (entity.Score < 0 || entity.Score > 100)
                    errors.Add($"Score 0-100 arasında olmalı - Entity: {entity.Id}, Score: {entity.Score}");
            }

            return errors;
        }

        /// <summary>
        /// Criterion feedback insert için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForCriterionFeedbackInsert(List<CriterionFeedbackEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.CriterionId))
                    errors.Add($"CriterionId boş olamaz - Entity: {entity.Id}");

                if (string.IsNullOrEmpty(entity.SubmissionId))
                    errors.Add($"SubmissionId boş olamaz - Entity: {entity.Id}");

                if (entity.Score < 0 || entity.Score > 100)
                    errors.Add($"Score 0-100 arasında olmalı - Entity: {entity.Id}, Score: {entity.Score}");
            }

            return errors;
        }

        /// <summary>
        /// Feedback update için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForFeedbackUpdate(List<SubmissionFeedbackEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.Id))
                    errors.Add("Update için entity Id boş olamaz");

                if (string.IsNullOrEmpty(entity.SubmissionId))
                    errors.Add($"SubmissionId boş olamaz - Entity: {entity.Id}");

                if (!entity.IsActive)
                    errors.Add($"Inactive entity güncellenemez - Entity: {entity.Id}");
            }

            return errors;
        }

        /// <summary>
        /// Feedback delete için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForFeedbackDelete(List<SubmissionFeedbackEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.Id))
                    errors.Add("Delete için entity Id boş olamaz");

                if (entity.Status == "Approved")
                    errors.Add($"Approved feedback silinemez - Entity: {entity.Id}");
            }

            return errors;
        }

        #endregion

        #endregion
    }
}
